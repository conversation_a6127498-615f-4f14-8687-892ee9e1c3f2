<div class="w-full bg-white p-3 rounded-lg">
  <div class="w-full flex items-center justify-between">
    <h6>Billing Section</h6>
    <button *ngIf="showActions" pButton icon="pi pi-times" iconPos="right" severity="danger" size="small"
      [outlined]="true"
      [label]="'Clear Cart'"
      class="hidden sm:inline-flex"
      (click)="clearCart()"></button>
    <!-- Mobile version with icon only -->
    
  </div>
  <div class="w-full mt-3">
    <div class="w-full" *ngIf="showActions">
      <div class="w-full">
        <p-autoComplete
          [(ngModel)]="products"
          [suggestions]="filteredProducts"
          (completeMethod)="onSearchChange($event)"
          (keyup.enter)="addToCart(filteredProducts[0])"
          field="name"
          [dropdown]="false"
          [forceSelection]="true"
          placeholder="Search products..."
          (onSelect)="addToCart(products)"
          (onClear)="onClearSearch()"
          class="w-full h-[100px]"
          minLength="3"
          autoHighlight="true"
          inputStyleClass="h-[50px] w-full"
          panelStyleClass="max-h-[300px] !w-[350px]"
          [panelStyle]="{ 'overflow-y': 'auto', 'width': '100%' }"
          >

          <!-- Custom Template for Suggestions -->
          <ng-template let-product pTemplate="item" >
            <div class="py-2 px-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer whitespace-normal">
              <app-product
                [bgColor]="''"
                [showPrice]="true"
                imageWidth="40px"
                imageHeight="40px"
                [showSku]="true"
                class="w-full"
                [product]="product">
              </app-product>
            </div>
          </ng-template>
        </p-autoComplete>
      </div>
    </div>

    <div class="w-full mt-3" [ngStyle]="{ 'min-height': cartItems.length > 0 ? '300px' : '0' }">
      <ng-container *ngIf="cartItems.length > 0">
        <p-table [value]="cartItems" stripedRows styleClass="w-full billing-table" class="rounded-md"
          [scrollable]="true" scrollHeight="500px"
          [responsiveLayout]="'scroll'">
          <ng-template #header>
            <tr class="items-center justify-center">
              <th class="text-sm">Item</th>
              <th class="text-sm">Quantity</th>
              <th class="text-sm" style="min-width: 100px">Price</th>
              <th class="text-sm" *ngIf="showActions">Delete</th>
            </tr>
          </ng-template>
          <ng-template #body let-product>
            <tr class="items-center justify-center">
              <td class="text-xs sm:text-sm">
                <app-product
                  [bgColor]="''"
                  [showPrice]="false"
                  imageWidth="40px"
                  imageHeight="40px"
                  class="w-full"
                  [product]="product">
                </app-product>
              </td>
              <td class="text-xs sm:text-sm">
                <ng-container *ngIf="quantityEdit">
                  <p-inputnumber
                    (onInput)="updateQuantity($event, product)"
                    [(ngModel)]="product.quantity"
                    [showButtons]="true"
                    buttonLayout="horizontal"
                    spinnerMode="horizontal"
                    inputId="horizontal"
                    [inputStyle]="{ width: '2.5rem', height: '1.8rem', textAlign: 'center', fontSize: '0.75rem' }">
                    <ng-template #incrementbuttonicon>
                      <span class="pi pi-plus text-xs"></span>
                    </ng-template>
                    <ng-template #decrementbuttonicon>
                      <span class="pi pi-minus text-xs"></span>
                    </ng-template>
                  </p-inputnumber>
                </ng-container>
                <ng-container *ngIf="!quantityEdit">
                  {{product.quantity}}
                </ng-container>
              </td>
              <td class="text-xs sm:text-sm" style="min-width: 80px">
                <span class="text-orange-500 font-semibold text-xs sm:text-sm">₹{{product.price?.toFixed(2)}}</span>
              </td>
              <td class="text-xs sm:text-sm">
                <button *ngIf="showActions"
                  class="pi pi-trash text-red-500 hover:text-red-700 p-1 text-xs sm:text-sm"
                  (click)="removeFromCart(product)"
                  pTooltip="Remove item"
                  tooltipPosition="left">
                </button>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </ng-container>
      <div class="w-full h-full flex flex-col items-center justify-center"
        *ngIf="cartItems.length == 0 && searchText.length == 0">
        <img src="https://img.freepik.com/premium-vector/payment-schedule-email-notification-computer_197170-647.jpg"
          alt="" class="w-[200px] h-[180px] sm:w-[330px] sm:h-[300px]" />
        <p class="text-center text-xs sm:text-sm flex flex-col gap-2 text-gray-500 px-4">
          <ng-container *ngIf="showActions; else invoiceText">
            <span class="font-semibold text-sm sm:text-lg">Add items to cart</span> to view
            billing details
          </ng-container>
          <ng-template #invoiceText>
            <span class="font-semibold text-sm sm:text-lg">Select an invoice</span> to view
            order details
          </ng-template>
        </p>
      </div>
      <app-no-data *ngIf="searchText.length > 2 && filteredProducts.length == 0"
        [ngStyle]="{'height': '250px'}"
        image="https://cdni.iconscout.com/illustration/free/thumb/free-empty-cart-illustration-download-in-svg-png-gif-file-formats--is-explore-box-states-pack-design-development-illustrations-3385483.png?f=webp"></app-no-data>
    </div>
    <p-divider />
    <div class="w-full mt-5">
      <p class="w-full flex justify-between mb-2 mt-0 text-xs sm:text-sm">
        <span>Total Items:</span>
        <span class="font-medium">{{cartItems.length}}</span>
      </p>
      <p class="w-full flex justify-between mb-2 text-xs sm:text-sm">
        <span>Sub Total:</span>
        <span class="font-medium">₹{{getSubTotal().toFixed(2)}}</span>
      </p>
      <p class="w-full flex justify-between mb-2 text-xs sm:text-sm">
        <span>Discount:</span>
        <span class="font-medium">₹{{getDiscount().toFixed(2)}}</span>
      </p>
      <p class="w-full flex justify-between mb-2 font-bold text-sm sm:text-base">
        <span>Grand Total:</span>
        <span>₹{{getGrandTotal().toFixed(2)}}</span>
      </p>
    </div>

    <!-- Checkout Button -->
    <div class="w-full flex justify-end mt-3" *ngIf="showActions && cartItems.length > 0">
      <button
        class="w-full"
        pButton
        icon="pi pi-check"
        severity="success"
        [outlined]="true"
        label="Checkout"
        [loading]="isProcessingCheckout"
        [disabled]="isProcessingCheckout"
        (click)="confirmCheckout()">
      </button>
    </div>
    <!-- Payment Modal -->
    <app-payment-modal
      [(visible)]="showConfirmDialog"
      [totalAmount]="getGrandTotal()"
      [isProcessing]="isProcessingCheckout"
      title="Confirm Checkout"
      confirmText="Are you sure you want to proceed with checkout?"
      confirmButtonLabel="Confirm Payment"
      cancelButtonLabel="Cancel"
      (confirm)="onPaymentConfirm($event)"
      (cancel)="onPaymentCancel()">
    </app-payment-modal>

    <!-- Loading State -->
    <div class="w-full flex items-center justify-center py-4" *ngIf="isProcessingCheckout && showActions">
      <p-progressSpinner
        [style]="{'width': '30px', 'height': '30px'}"
        strokeWidth="4"
        animationDuration="1s">
      </p-progressSpinner>
      <span class="ml-3 text-sm text-gray-600">Processing order...</span>
    </div>
  </div>
</div>
