import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { InputNumberModule } from 'primeng/inputnumber';
import { DialogModule } from 'primeng/dialog';
import { RadioButtonModule } from 'primeng/radiobutton';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { CommonService } from '../../services/common';

export interface PaymentData {
  selectedPaymentMethod: string;
  cashAmount: number;
  upiAmount: number;
  upiId: string;
  cardAmount: number;
  cardType: string;
  partialPaymentAmount: number;
}

@Component({
  selector: 'app-payment-modal',
  standalone: true,
  templateUrl: './payment-modal.html',
  imports: [
    CommonModule,
    FormsModule,
    InputNumberModule,
    DialogModule,
    RadioButtonModule,
    ButtonModule,
    InputTextModule,
  ],
})
export class PaymentModalComponent implements OnChanges {
  @Input() visible = false;
  @Input() totalAmount = 0;
  @Input() isProcessing = false;
  @Input() title = 'Confirm Checkout';
  @Input() confirmText = 'Are you sure you want to proceed with checkout?';
  @Input() confirmButtonLabel = 'Confirm';
  @Input() cancelButtonLabel = 'Cancel';

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() confirm = new EventEmitter<PaymentData>();
  @Output() cancel = new EventEmitter<void>();

  // Payment properties
  selectedPaymentMethod: string = 'cash';
  cashAmount = 0;
  upiAmount = 0;
  upiId = '';
  cardAmount = 0;
  cardType = 'credit';
  partialPaymentAmount = 0;
  remainingAmount = 0;

  constructor(private commonService: CommonService) {}

  // Expose Math for template use
  Math = Math;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['totalAmount'] && this.totalAmount) {
      this.resetPaymentAmounts();
    }
  }

  resetPaymentAmounts() {
    this.cashAmount = this.totalAmount;
    this.upiAmount = this.totalAmount;
    this.cardAmount = this.totalAmount;
    this.partialPaymentAmount = 0;
    this.remainingAmount = this.totalAmount;
  }

  handlePaymentMethodChange(method: string) {
    this.selectedPaymentMethod = method;
    if (method === 'partial') {
      this.remainingAmount = this.totalAmount;
      this.partialPaymentAmount = 0;
    } else {
      this.resetPaymentAmounts();
    }
  }

  updatePartialPayment(amount: number | string | null) {
    const validAmount = typeof amount === 'string' ? parseFloat(amount) || 0 : amount || 0;
    if (validAmount > this.totalAmount) {
      this.commonService.toast({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Partial payment cannot exceed total amount'
      });
      this.partialPaymentAmount = this.totalAmount;
      this.remainingAmount = 0;
    } else {
      this.partialPaymentAmount = validAmount;
      this.remainingAmount = this.totalAmount - validAmount;
    }
  }

  onConfirm() {
    const paymentData: PaymentData = {
      selectedPaymentMethod: this.selectedPaymentMethod,
      cashAmount: this.cashAmount,
      upiAmount: this.upiAmount,
      upiId: this.upiId,
      cardAmount: this.cardAmount,
      cardType: this.cardType,
      partialPaymentAmount: this.partialPaymentAmount,
    };
    this.confirm.emit(paymentData);
  }

  onCancel() {
    this.visible = false;
    this.visibleChange.emit(false);
    this.cancel.emit();
  }

  onDialogHide() {
    this.visible = false;
    this.visibleChange.emit(false);
  }

  getChange(): number {
    switch (this.selectedPaymentMethod) {
      case 'cash':
        return this.cashAmount - this.totalAmount;
      case 'upi':
        return this.upiAmount - this.totalAmount;
      case 'card':
        return this.cardAmount - this.totalAmount;
      default:
        return 0;
    }
  }
}
