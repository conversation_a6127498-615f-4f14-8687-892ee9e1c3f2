# Payment Modal Component

A reusable payment modal component for handling various payment methods including Cash, UPI, Card, and Partial payments.

## Features

- **Multiple Payment Methods**: Cash, UPI, Card (Credit/Debit), Partial Payment
- **Input Validation**: Automatic validation for payment amounts
- **Customizable**: Configurable titles, messages, and button labels
- **Type Safety**: Full TypeScript support with proper interfaces
- **Responsive Design**: Mobile-friendly with Tailwind CSS

## Usage

### Basic Implementation

```typescript
import { PaymentModalComponent, PaymentData } from '../payment-modal/payment-modal';

@Component({
  imports: [PaymentModalComponent],
  // ... other component config
})
export class YourComponent {
  showPaymentModal = false;
  totalAmount = 1500.00;
  isProcessing = false;

  onPaymentConfirm(paymentData: PaymentData) {
    console.log('Payment Data:', paymentData);
    this.isProcessing = true;
    
    // Process payment logic here
    this.processPayment(paymentData);
  }

  onPaymentCancel() {
    this.showPaymentModal = false;
  }

  private processPayment(paymentData: PaymentData) {
    // Your payment processing logic
    // paymentData contains all payment information
  }
}
```

### HTML Template

```html
<app-payment-modal
  [(visible)]="showPaymentModal"
  [totalAmount]="totalAmount"
  [isProcessing]="isProcessing"
  title="Confirm Payment"
  confirmText="Please confirm your payment details"
  confirmButtonLabel="Pay Now"
  cancelButtonLabel="Cancel"
  (confirm)="onPaymentConfirm($event)"
  (cancel)="onPaymentCancel()">
</app-payment-modal>
```

## Input Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `visible` | `boolean` | `false` | Controls modal visibility |
| `totalAmount` | `number` | `0` | Total amount to be paid |
| `isProcessing` | `boolean` | `false` | Shows loading state |
| `title` | `string` | `'Confirm Checkout'` | Modal header title |
| `confirmText` | `string` | `'Are you sure...'` | Confirmation message |
| `confirmButtonLabel` | `string` | `'Confirm'` | Confirm button text |
| `cancelButtonLabel` | `string` | `'Cancel'` | Cancel button text |

## Output Events

| Event | Type | Description |
|-------|------|-------------|
| `visibleChange` | `boolean` | Emitted when modal visibility changes |
| `confirm` | `PaymentData` | Emitted when payment is confirmed |
| `cancel` | `void` | Emitted when payment is cancelled |

## PaymentData Interface

```typescript
interface PaymentData {
  selectedPaymentMethod: string;  // 'cash' | 'upi' | 'card' | 'partial'
  cashAmount: number;
  upiAmount: number;
  upiId: string;
  cardAmount: number;
  cardType: string;              // 'credit' | 'debit'
  partialPaymentAmount: number;
}
```

## Payment Methods

### Cash Payment
- Enter cash amount (can be more than total for change calculation)
- Shows change amount automatically

### UPI Payment
- Enter UPI amount
- Provide UPI ID for transaction

### Card Payment
- Select Credit or Debit card
- Enter card payment amount

### Partial Payment
- Enter cash amount for partial payment
- Remaining amount automatically calculated for card payment

## Styling

The component uses Tailwind CSS classes and is fully responsive. You can customize the appearance by modifying the CSS classes in the template.

## Dependencies

- PrimeNG (Dialog, InputNumber, RadioButton, Button)
- Angular Forms (FormsModule)
- CommonService (for toast notifications)
